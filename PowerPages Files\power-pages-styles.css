/* Power Pages Global Styles - <PERSON>sler Branding */

:root {
  /* Primary Colors - <PERSON>sler Red Theme */
  --primary-red: #c41e3a;
  --primary-red-dark: #a01729;
  --primary-red-light: #f8d7da;
  
  /* Neutral Colors */
  --color-white: #ffffff;
  --color-black: #000000;
  --gray-light: #f8f9fa;
  --gray-medium: #6c757d;
  --gray-dark: #343a40;
  --gray-border: #dee2e6;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Global Styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  color: var(--gray-dark);
  background-color: var(--color-white);
}

/* Container Styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.container-flex {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 80vh;
  padding: var(--spacing-xl) var(--spacing-md);
}

/* Card Styles */
.card {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-border);
  overflow: hidden;
}

.card-header {
  background-color: var(--color-white);
  border-bottom: 2px solid var(--primary-red);
  padding: var(--spacing-lg);
  text-align: center;
}

.card-body {
  padding: var(--spacing-xl);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--gray-dark);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

/* Form Styles */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--gray-dark);
  font-weight: 600;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--gray-dark);
  background-color: var(--color-white);
  border: 2px solid var(--gray-border);
  border-radius: var(--radius-md);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: var(--primary-red);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(196, 30, 58, 0.25);
}

.form-control.is-valid {
  border-color: #28a745;
}

.form-control.is-invalid {
  border-color: var(--primary-red);
}

.form-text {
  margin-top: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--gray-medium);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--primary-red);
}

/* Button Styles */
.btn {
  display: inline-block;
  font-weight: 600;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  border: 2px solid transparent;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: all 0.15s ease-in-out;
}

.btn-primary {
  color: var(--color-white);
  background-color: var(--primary-red);
  border-color: var(--primary-red);
}

.btn-primary:hover {
  background-color: var(--primary-red-dark);
  border-color: var(--primary-red-dark);
  color: var(--color-white);
}

.btn-outline-primary {
  color: var(--primary-red);
  background-color: transparent;
  border-color: var(--primary-red);
}

.btn-outline-primary:hover {
  background-color: var(--primary-red);
  border-color: var(--primary-red);
  color: var(--color-white);
}

.btn-outline-secondary {
  color: var(--gray-medium);
  background-color: transparent;
  border-color: var(--gray-border);
}

.btn-outline-secondary:hover {
  background-color: var(--gray-light);
  border-color: var(--gray-medium);
  color: var(--gray-dark);
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.w-100 {
  width: 100% !important;
}

/* Input Group Styles */
.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group .form-control {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}

.input-group .btn {
  position: relative;
  z-index: 2;
}

/* Alert Styles */
.alert {
  position: relative;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
}

.alert-danger {
  color: var(--primary-red);
  background-color: var(--primary-red-light);
  border-color: var(--primary-red);
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

/* Notification System Styles */
.notification-container {
  position: relative;
  margin-bottom: var(--spacing-md);
}

.notification {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--radius-md);
  border-left: 4px solid;
  box-shadow: var(--shadow-sm);
  transition: opacity 0.3s ease;
}

.notification-fade-in {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.notification-icon {
  margin-right: var(--spacing-sm);
  font-size: 1.25rem;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.notification-message {
  margin: 0;
  line-height: 1.5;
}

.notification-success {
  background-color: #f0f9ff;
  border-left-color: #10b981;
  color: #065f46;
}

.notification-error {
  background-color: var(--primary-red-light);
  border-left-color: var(--primary-red);
  color: var(--primary-red-dark);
}

.notification-warning {
  background-color: #fef3c7;
  border-left-color: #f59e0b;
  color: #92400e;
}

.notification-info {
  background-color: #eff6ff;
  border-left-color: #3b82f6;
  color: #1e40af;
}

.notification-loading {
  background-color: var(--gray-light);
  border-left-color: var(--primary-red);
  color: var(--gray-dark);
}

/* Utility Classes */
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.text-center {
  text-align: center !important;
}

.text-start {
  text-align: left !important;
}

.text-end {
  text-align: right !important;
}

.mt-3 {
  margin-top: var(--spacing-md) !important;
}

.mb-3 {
  margin-bottom: var(--spacing-md) !important;
}

.fw-bold {
  font-weight: 600 !important;
}

/* Form Check Styles */
.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: var(--spacing-sm);
}

.form-check-input {
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  margin-left: -1.5em;
  vertical-align: top;
  background-color: var(--color-white);
  border: 2px solid var(--gray-border);
  border-radius: var(--radius-sm);
}

.form-check-input:checked {
  background-color: var(--primary-red);
  border-color: var(--primary-red);
}

.form-check-label {
  color: var(--gray-dark);
  cursor: pointer;
}

/* Spinner Styles */
.spinner-border {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: text-bottom;
  border: 0.125em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
  width: 0.875rem;
  height: 0.875rem;
  border-width: 0.125em;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-flex {
    padding: var(--spacing-md);
    min-height: auto;
  }
  
  .card-body {
    padding: var(--spacing-lg);
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}

@media (max-width: 480px) {
  .container-flex {
    padding: var(--spacing-sm);
  }
  
  .card-body {
    padding: var(--spacing-md);
  }
  
  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}
