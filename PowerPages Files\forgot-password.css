.forgot-password-container {
    max-width: 500px;
}

.forgot-password-container h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 2px;
    background-color: var(--primary-red);
    margin: 1rem auto;
}

#resetButton {
    background-color: var(--primary-red);
    color: var(--color-white);
    border: 2px solid var(--primary-red);
}

#resetButton:hover {
    background-color: var(--primary-red-dark);
    border-color: var(--primary-red-dark);
}

.text-center.mt-3 .btn-outline-secondary:hover {
    background-color: var(--gray-light);
    border-color: var(--gray-medium);
    color: var(--gray-dark);
    text-decoration: none;
}

.text-center.mt-3 a:not(.btn):hover {
    color: var(--primary-red-dark);
    text-decoration: underline;
}

/* Notification System Integration */
.forgot-password-container .notification-container {
    margin-bottom: var(--spacing-md);
}

.forgot-password-container .notification {
    margin-bottom: var(--spacing-sm);
}

/* Ensure notifications don't interfere with form layout */
.forgot-password-container #forgotPasswordForm {
    margin-top: var(--spacing-sm);
}

/* Legacy notification compatibility */
.forgot-password-container #errorMessage,
.forgot-password-container #successMessage {
    margin-bottom: var(--spacing-md);
}

@media (max-width: 768px) {
    .forgot-password-container {
        max-width: 95%;
    }

    .forgot-password-container .notification {
        padding: var(--spacing-sm);
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .forgot-password-container h2::after {
        width: 40px;
        margin: 0.75rem auto;
    }
}
