// Unified Notification System for Power Pages
const NotificationSystem = {
  config: { defaultTimeout: 5000, fadeInDuration: 300, fadeOutDuration: 300, autoHide: true, showIcons: true },
  icons: { success: 'fas fa-check-circle', error: 'fas fa-exclamation-circle', warning: 'fas fa-exclamation-triangle', info: 'fas fa-info-circle', loading: 'fas fa-spinner fa-spin' },

  show(type, message, options = {}) {
    const settings = { ...this.config, ...options };
    const container = this.getNotificationContainer(settings.containerId);
    if (!container) {
      console.warn('Notification container not found');
      return;
    }

    if (settings.clearExisting !== false) this.clear(container);

    const notification = this.createNotification(type, message, settings);
    container.appendChild(notification);
    notification.classList.add('notification-fade-in');

    if (settings.autoHide && settings.timeout > 0) {
      setTimeout(() => this.hide(notification), settings.timeout || this.config.defaultTimeout);
    }

    this.setAriaLiveRegion(notification, type);
    return notification;
  },

  showSuccess(message, options = {}) {
    return this.show('success', message, { ...options, timeout: options.timeout || 4000, title: options.title || 'Success' });
  },

  showError(message, options = {}) {
    return this.show('error', message, { ...options, timeout: options.timeout || 0, title: options.title || 'Error' });
  },

  showWarning(message, options = {}) {
    return this.show('warning', message, { ...options, timeout: options.timeout || 6000, title: options.title || 'Warning' });
  },

  showInfo(message, options = {}) {
    return this.show('info', message, { ...options, timeout: options.timeout || 5000, title: options.title || 'Information' });
  },

  showLoading(message, options = {}) {
    return this.show('loading', message, { ...options, timeout: 0, title: options.title || 'Loading', autoHide: false });
  },

  createNotification: function(type, message, settings) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
    notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

    let content = '';
    if (settings.showIcons && this.icons[type]) {
      content += `<div class="notification-icon"><i class="${this.icons[type]}" aria-hidden="true"></i></div>`;
    }

    content += '<div class="notification-content">';
    if (settings.title) {
      content += `<div class="notification-title">${this.escapeHtml(settings.title)}</div>`;
    }
    content += `<div class="notification-message">${this.escapeHtml(message)}</div>`;
    content += '</div>';

    notification.innerHTML = content;
    if (settings.compact) {
      notification.classList.add('notification-compact');
    }

    return notification;
  },

  getNotificationContainer: function(containerId) {
    if (containerId) {
      return document.getElementById(containerId);
    }

    const commonIds = ['notificationContainer', 'messageContainer', 'alertContainer'];
    for (const id of commonIds) {
      const container = document.getElementById(id);
      if (container) return container;
    }

    const jquerySelectors = ['#errorMessage', '#successMessage', '#messageContent'];
    for (const selector of jquerySelectors) {
      const element = $(selector);
      if (element.length > 0) {
        return element[0];
      }
    }

    return this.createDefaultContainer();
  },

  createDefaultContainer: function() {
    const container = document.createElement('div');
    container.id = 'notificationContainer';
    container.className = 'notification-container';

    const mainContent = document.querySelector('.card-body, .container, main, body');
    if (mainContent) {
      mainContent.insertBefore(container, mainContent.firstChild);
      return container;
    }

    return null;
  },

  hide: function(notification) {
    if (!notification || !notification.parentNode) return;
    notification.style.transition = `opacity ${this.config.fadeOutDuration}ms ease`;
    notification.style.opacity = '0';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, this.config.fadeOutDuration);
  },

  clear: function(container) {
    if (!container) {
      container = this.getNotificationContainer();
    }

    if (container) {
      const notifications = container.querySelectorAll('.notification, .message, .alert');
      notifications.forEach(notification => {
        this.hide(notification);
      });
    }

    $('#errorMessage, #successMessage').hide();
  },

  setAriaLiveRegion: function(notification, type) {
    if (type === 'error') {
      notification.setAttribute('aria-live', 'assertive');
      notification.setAttribute('role', 'alert');
    } else {
      notification.setAttribute('aria-live', 'polite');
      notification.setAttribute('role', 'status');
    }
  },

  escapeHtml: function(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
};

const SecureConfig = {
  getFunctionUrl: (functionName = 'PasswordService') => {
    const baseUrl = window.appConfig?.functionUrl;
    return baseUrl ? `${baseUrl}/api/${functionName}` : null;
  },

  getFunctionKey: () => {
    const functionKey = window.appConfig?.passwordFunctionKey;
    return (!functionKey || functionKey.includes('ERROR_MISSING')) ? null : functionKey;
  },

  buildSecureUrl: (functionName, operation) => {
    const baseUrl = SecureConfig.getFunctionUrl(functionName);
    const functionKey = SecureConfig.getFunctionKey();
    return (!baseUrl || !functionKey) ? null : `${baseUrl}?operation=${operation}&code=${functionKey}`;
  }
};

// Add remaining NotificationSystem methods
NotificationSystem.handleError = function(error, context = {}) {
  console.error('Error in context:', context, error);
  if (error.status) return this.handleHttpError(error, context);

  const errorTypes = {
    fetch: () => error instanceof TypeError && error.message.includes('fetch'),
    timeout: () => error.name === 'AbortError' || error.message.includes('timeout'),
    config: () => error.message.includes('configuration') || error.message.includes('missing'),
    rateLimit: () => error.message.includes('rate limit') || error.message.includes('429'),
    auth: () => error.message.includes('401') || error.message.includes('unauthorized')
  };

  const errorMessages = {
    fetch: { msg: 'Network connection failed. Please check your internet connection and try again.', title: 'Connection Error' },
    timeout: { msg: 'Request timed out. Please check your connection and try again. If the problem persists, try refreshing the page.', title: 'Request Timeout' },
    config: { msg: 'System configuration error. Please contact support if this persists.', title: 'Configuration Error' },
    rateLimit: { msg: error.message, title: 'Rate Limit', timeout: 10000 },
    auth: { msg: 'Authentication failed. Please refresh the page and try again.', title: 'Authentication Error' }
  };

  for (const [type, check] of Object.entries(errorTypes)) {
    if (check()) {
      const { msg, title, timeout = 0 } = errorMessages[type];
      return type === 'rateLimit' ? this.showWarning(msg, { title, timeout }) : this.showError(msg, { title, timeout });
    }
  }

  return this.showError(error.message || 'An unexpected error occurred. Please try again.', { title: 'Error', timeout: 0 });
};

NotificationSystem.handleHttpError = function(error, context = {}) {
  const httpErrors = {
    400: { msg: 'Invalid request. Please check your input and try again.', title: 'Invalid Request' },
    401: { msg: 'Authentication required. Please refresh the page and try again.', title: 'Authentication Required' },
    403: { msg: 'Access denied. You do not have permission to perform this action.', title: 'Access Denied' },
    404: { msg: 'Service not found. Please contact support if this persists.', title: 'Service Unavailable' },
    429: { msg: 'Too many requests. Please wait a moment before trying again.', title: 'Rate Limit Exceeded', timeout: 15000 },
    500: { msg: 'A server error occurred. Please try again later or contact support.', title: 'Server Error' }
  };

  const status = error.status;

  if (status === 409) {
    const conflictMsg = error.message || 'A conflict occurred. Please review your input.';
    if (conflictMsg.toLowerCase().includes('password') &&
        (conflictMsg.toLowerCase().includes('reuse') || conflictMsg.toLowerCase().includes('history') || conflictMsg.toLowerCase().includes('recent'))) {
      return this.showError(conflictMsg, { title: 'Password Reuse Detected', timeout: 0 });
    }
    return this.showError(conflictMsg, { title: 'Conflict', timeout: 0 });
  }

  const errorInfo = httpErrors[status] || (status >= 500 ? httpErrors[500] : { msg: 'Request failed. Please try again or contact support if the problem persists.', title: 'Request Failed' });
  const { msg, title, timeout = 0 } = errorInfo;

  return status === 429 ? this.showWarning(msg, { title, timeout }) : this.showError(msg, { title, timeout });
};

NotificationSystem.validateConfiguration = function(requiredConfig = []) {
  const missing = requiredConfig.filter(key => !window.appConfig || !window.appConfig[key]);
  if (missing.length > 0) {
    this.showError(`Missing configuration: ${missing.join(', ')}. Please contact support.`, { title: 'Configuration Error', timeout: 0 });
    return false;
  }
  return true;
};

NotificationSystem.validateDOMElements = function(elementSelectors = []) {
  const missing = elementSelectors.filter(selector => !document.querySelector(selector));
  if (missing.length > 0) {
    console.error('Missing DOM elements:', missing);
    this.showError('Page elements missing. Please refresh the page.', { title: 'Page Error', timeout: 0 });
    return false;
  }
  return true;
};

NotificationSystem.checkBrowserCompatibility = function() {
  const features = [
    { check: () => typeof fetch === 'undefined', name: 'Fetch API' },
    { check: () => typeof Promise === 'undefined', name: 'Promise support' },
    { check: () => typeof URLSearchParams === 'undefined', name: 'URL Parameters' },
    { check: () => !document.querySelector || !document.querySelectorAll, name: 'Modern DOM methods' }
  ];

  try {
    if (typeof localStorage === 'undefined' || typeof sessionStorage === 'undefined') {
      features.push({ check: () => true, name: 'Local Storage' });
    }
  } catch (e) {
    features.push({ check: () => true, name: 'Local Storage' });
  }

  const missing = features.filter(f => f.check()).map(f => f.name);

  if (missing.length > 0) {
    const message = `Your browser is missing required features: ${missing.join(', ')}. Please update to a modern browser (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+) to use this application.`;
    try {
      this.showError(message, { title: 'Browser Compatibility Issue', timeout: 0 });
    } catch (e) {
      alert('Browser Compatibility Issue: ' + message);
    }
    return false;
  }
  return true;
};

const PASSWORD_SERVICE_URL = SecureConfig.getFunctionUrl('PasswordService');
const APPLICATION_NAME = window.appConfig?.applicationName || "";
const PASSWORD_REUSE_FLAG = 'PASSWORD_REUSE';
const urlParams = new URLSearchParams(window.location.search);
const RESET_TOKEN = urlParams.get('token');

if (!PASSWORD_SERVICE_URL) console.error("PasswordService URL not configured");

// Token validation will be handled in the main document ready function

const InputSanitizer = {
  sanitizeInput: input => typeof input === 'string' ? input.trim().replace(/[<>"'&]/g, '').substring(0, 256) : '',

  validatePassword: password => {
    const minLength = 8;
    return password.length >= minLength &&
           /[A-Z]/.test(password) &&
           /[a-z]/.test(password) &&
           /\d/.test(password) &&
           /[!@#$%^&*(),.?":{}|<>]/.test(password);
  }
};

const errMsgDiv = $('#errorMessage');
const successMsgDiv = $('#successMessage');
const submitButton = $('#submitButton');
const passwordForm = $('#passwordForm');
const verificationCodeInput = $('#verificationCode');
const newPasswordInput = $('#newPassword');
const confirmPasswordInput = $('#confirmPassword');
const toggleButtons = {
  newPassword: $('#toggleNewPassword'),
  confirmPassword: $('#toggleConfirmPassword')
};
// Unified notification functions
const showMessage = (message, isError = true, timeout = 0) => {
  const type = isError ? 'error' : 'success';
  const options = timeout > 0 ? { timeout } : {};
  return NotificationSystem.show(type, message, options);
};

const showPasswordReuseError = message => {
  NotificationSystem.showError(`Password Cannot Be Reused: ${message}`, { title: 'Password Reuse Detected', timeout: 0 });

  $('#newPassword').val('').focus();
  $('#confirmPassword').val('');
  $('#newPassword, #confirmPassword').removeClass('is-invalid');
  $('.invalid-feedback').text('');
};

const clearMessages = () => {
  NotificationSystem.clear();
  errMsgDiv.add(successMsgDiv).hide();
};

const showSuccess = (message, timeout = 4000) => NotificationSystem.showSuccess(message, { timeout });
const showError = message => NotificationSystem.showError(message);
const showLoading = message => NotificationSystem.showLoading(message);

const showSuccessNotification = (title, message) => {
  const modalHtml = `
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-success text-white">
            <h5 class="modal-title" id="successModalLabel">
              <i class="fas fa-check-circle me-2"></i>${title}
            </h5>
          </div>
          <div class="modal-body text-center">
            <div class="mb-3">
              <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
            </div>
            <p class="mb-0">${message}</p>
            <p class="text-muted mt-2">You will be redirected shortly...</p>
          </div>
        </div>
      </div>
    </div>
  `;

  $('#successModal').remove();
  $('body').append(modalHtml);

  const modal = new bootstrap.Modal(document.getElementById('successModal'), { backdrop: 'static', keyboard: false });
  modal.show();
  setTimeout(() => modal.hide(), 2500);
};
function togglePasswordVisibility(inputId, toggleButton) {
  const input = $(`#${inputId}`);
  const icon = toggleButton.find('i');
  
  if (input.attr('type') === 'password') {
    input.attr('type', 'text');
    icon.removeClass('fa-eye').addClass('fa-eye-slash');
  } else {
    input.attr('type', 'password');
    icon.removeClass('fa-eye-slash').addClass('fa-eye');
  }
}


function validateNewPassword() {
  const password = newPasswordInput.val();
  const passwordError = $('#passwordError');

  // Clear previous validation state
  newPasswordInput.removeClass('is-invalid is-valid');
  newPasswordInput.closest('.form-group').removeClass('has-error');

  if (!password) {
    passwordError.text('Password is required');
    newPasswordInput.addClass('is-invalid');
    newPasswordInput.closest('.form-group').addClass('has-error');
    newPasswordInput.focus();
    return false;
  }

  if (!InputSanitizer.validatePassword(password)) {
    passwordError.text('Password must be at least 8 characters with uppercase, lowercase, number, and special character');
    newPasswordInput.addClass('is-invalid');
    newPasswordInput.closest('.form-group').addClass('has-error');
    newPasswordInput.focus();
    return false;
  }

  passwordError.text('');
  newPasswordInput.removeClass('is-invalid').addClass('is-valid');
  return true;
}

function validateConfirmPassword() {
  const password = newPasswordInput.val();
  const confirmPassword = confirmPasswordInput.val();
  const confirmPasswordError = $('#confirmPasswordError');
  
  if (!confirmPassword) {
    confirmPasswordError.text('Please confirm your password');
    confirmPasswordInput.addClass('is-invalid');
    return false;
  }
  
  if (password !== confirmPassword) {
    confirmPasswordError.text('Passwords do not match');
    confirmPasswordInput.addClass('is-invalid');
    return false;
  }
  
  confirmPasswordError.text('');
  confirmPasswordInput.removeClass('is-invalid');
  return true;
}

function validateVerificationCode() {
  const verificationCode = verificationCodeInput.val();
  const verificationCodeError = $('#verificationCodeError');

  if (!verificationCode) {
    verificationCodeError.text('Verification code is required');
    verificationCodeInput.addClass('is-invalid');
    return false;
  }

  if (!/^\d{6}$/.test(verificationCode)) {
    verificationCodeError.text('Verification code must be exactly 6 digits');
    verificationCodeInput.addClass('is-invalid');
    return false;
  }

  verificationCodeError.text('');
  verificationCodeInput.removeClass('is-invalid');
  return true;
}

function validateForm() {
  const isVerificationCodeValid = validateVerificationCode();
  const isNewPasswordValid = validateNewPassword();
  const isConfirmPasswordValid = validateConfirmPassword();

  return isVerificationCodeValid && isNewPasswordValid && isConfirmPasswordValid;
}

async function performPasswordReset(newPassword, verificationCode) {
  try {
    const sanitizedPassword = InputSanitizer.sanitizeInput(newPassword);
    const sanitizedVerificationCode = InputSanitizer.sanitizeInput(verificationCode);

    if (!InputSanitizer.validatePassword(sanitizedPassword)) {
      throw new Error('Invalid password format');
    }

    if (!RESET_TOKEN) {
      throw new Error('Reset token is missing');
    }

    if (!sanitizedVerificationCode) {
      throw new Error('Verification code is required');
    }

    const requestBody = {
      token: RESET_TOKEN,
      verificationCode: sanitizedVerificationCode,
      newPassword: sanitizedPassword,
      applicationName: APPLICATION_NAME
    };

    const apiUrl = SecureConfig.buildSecureUrl('PasswordService', 'reset-complete');
    if (!apiUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }



    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest' // CSRF protection
      },
      body: JSON.stringify(requestBody)
    });

    console.log('API Response Status:', response.status, response.statusText);

    if (response.status === 429) {
      const responseText = await response.text();
      let result;
      try {
        result = JSON.parse(responseText);
        if (result.retryAfter) {
          const retryAfter = new Date(result.retryAfter);
          const waitTime = Math.ceil((retryAfter - new Date()) / 1000);
          throw new Error(`Too many requests. Please wait ${waitTime} seconds before trying again.`);
        }
      } catch (parseError) {
        throw new Error('Too many requests. Please wait a moment before trying again.');
      }
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    const responseText = await response.text();
    console.log('API Response Text:', responseText);

    let result;
    if (responseText) {
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON Parse Error:', parseError);
        throw new Error('Invalid response from server. Please try again.');
      }
    } else {
      throw new Error('Empty response from server. Please try again.');
    }

    if (response.ok) {
      const responseData = result.data || result;
      return {
        success: true,
        message: responseData.message || result.message || "Password updated successfully!",
        email: responseData.email || result.email,
        requiresLogout: responseData.requiresLogout || result.requiresLogout
      };
    } else if (response.status === 409 && result.action === "ShowBlockPage") {
      // Password reuse error - throw with special flag
      const error = new Error(result.userMessage || "Password has been used recently. Please choose a different password.");
      error.type = PASSWORD_REUSE_FLAG;
      throw error;
    } else {
      const errorMessage = result.message || (result.data && result.data.message) || "Password operation failed. Please try again.";
      throw new Error(errorMessage);
    }

  } catch (error) {
    console.error("Password operation error:", error);
    throw error;
  }
}

function initializeFormHandlers() {
  verificationCodeInput.blur(function() {
    validateVerificationCode();
  });

  verificationCodeInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#verificationCodeError').text('');
  });

  newPasswordInput.blur(function() {
    validateNewPassword();
  });

  confirmPasswordInput.blur(function() {
    validateConfirmPassword();
  });

  newPasswordInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#passwordError').text('');
  });

  confirmPasswordInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#confirmPasswordError').text('');
  });

  toggleButtons.newPassword.click(function() {
    togglePasswordVisibility('newPassword', $(this));
  });

  toggleButtons.confirmPassword.click(function() {
    togglePasswordVisibility('confirmPassword', $(this));
  });

  passwordForm.submit(async function(event) {
    event.preventDefault();
    clearMessages();

    try {
      if (!validateForm()) {
        return;
      }

      submitButton.prop("disabled", true);
      submitButton.text("Resetting...");

      const newPassword = newPasswordInput.val();
      const verificationCode = verificationCodeInput.val();

      showMessage("Resetting your password...", false);

      const result = await performPasswordReset(newPassword, verificationCode);

      if (result.success) {
        showMessage("Password reset successfully!", false);
        passwordForm[0].reset();

        $('input').removeClass('is-invalid');
        $('.invalid-feedback').text('');

        showSuccessNotification("Password Reset Successful!", "Your password has been reset successfully. You can now log in with your new password.");

        setTimeout(() => {
          window.location.href = '/?message=' + encodeURIComponent('Password reset successfully. You can now log in with your new password.');
        }, 3000);
      }

    } catch (error) {
      console.error("Form submission error:", error);

      if (error.type === PASSWORD_REUSE_FLAG) {
        showPasswordReuseError(error.message);
      } else {
        // Use enhanced error handling
        NotificationSystem.handleError(error, {
          operation: 'reset-password',
          verificationCode: verificationCodeInput.val()
        });
      }
    } finally {
      submitButton.prop("disabled", false);
      submitButton.text("Reset Password");
    }
  });
}

function initializeResetForm() {
  submitButton.text('Reset Password');
}

async function validateResetTokenAccess(token) {
  try {
    const secureUrl = SecureConfig.buildSecureUrl('PasswordService', 'validate-reset-token');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: token
      })
    });

    const result = await response.json();
    const data = result.data || result;

    if (!data.success) {
      showUnauthorizedAccess(data.message || 'Invalid or expired reset token.');
      return;
    }

    showTokenValidationSuccess();

  } catch (error) {
    console.error('Token validation error:', error);
    showUnauthorizedAccess('Unable to validate reset token. Please try again.');
  }
}

function showUnauthorizedAccess(message) {
  console.error('Access denied:', message);

  sessionStorage.setItem('resetError', JSON.stringify({
    message: message,
    timestamp: new Date().toISOString(),
    source: 'reset-token-validation'
  }));

  const errorPageUrl = '/Reset-Error/';

  try {
    window.location.href = errorPageUrl;
  } catch (error) {
    console.warn('Error page not found, redirecting to forgot password:', error);
    window.location.href = '/Forgot-Password/';
  }
}

function showTokenValidationSuccess() {
  const pageHeader = document.querySelector('.card-header h3');
  if (pageHeader) {
    pageHeader.innerHTML = '<i class="fas fa-check-circle text-success"></i> Reset Password <small class="text-success">(Token Verified)</small>';
  }

  // Initialize form handlers only after successful token validation
  initializeResetForm();
  initializeFormHandlers();
}

$(document).ready(function() {
  if (!NotificationSystem.checkBrowserCompatibility()) return;

  const requiredConfig = ['functionUrl', 'passwordFunctionKey'];
  if (NotificationSystem.validateConfiguration(requiredConfig)) {
    const requiredElements = ['#passwordForm', '#verificationCode', '#newPassword', '#confirmPassword'];
    if (NotificationSystem.validateDOMElements(requiredElements)) {
      // Check if we have a reset token
      if (RESET_TOKEN) {
        validateResetTokenAccess(RESET_TOKEN);
      } else {
        showUnauthorizedAccess('No reset token provided. Please use the link from your password reset email.');
      }
    }
  }
});
